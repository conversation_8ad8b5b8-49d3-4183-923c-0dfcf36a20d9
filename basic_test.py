"""
基础测试程序 - 测试打包后的基本功能
"""

import sys
import os

def main():
    print("=== 录屏软件基础测试 ===")
    print(f"Python版本: {sys.version}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"程序路径: {sys.argv[0]}")
    print()
    
    # 测试PyQt5
    try:
        print("测试PyQt5...")
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from PyQt5.QtCore import Qt
        print("✓ PyQt5导入成功")
        
        # 创建应用
        app = QApplication(sys.argv)
        print("✓ QApplication创建成功")
        
        # 显示消息框
        msg = QMessageBox()
        msg.setWindowTitle("测试")
        msg.setText("录屏软件测试成功！\n\n所有基本模块都能正常工作。")
        msg.setIcon(QMessageBox.Information)
        msg.exec_()
        
        print("✓ 测试完成")
        
    except Exception as e:
        print(f"✗ PyQt5测试失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")
        return
    
    # 测试OpenCV
    try:
        print("测试OpenCV...")
        import cv2
        print(f"✓ OpenCV版本: {cv2.__version__}")
    except Exception as e:
        print(f"✗ OpenCV测试失败: {e}")
    
    # 测试NumPy
    try:
        print("测试NumPy...")
        import numpy as np
        print(f"✓ NumPy版本: {np.__version__}")
    except Exception as e:
        print(f"✗ NumPy测试失败: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
