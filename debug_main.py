"""
调试版主程序 - 用于测试打包后的程序
"""

import os
import sys
import traceback
import logging

def setup_logging():
    """设置日志记录"""
    log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "debug.log")
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(__name__)

def main():
    """主函数"""
    logger = setup_logging()
    
    try:
        logger.info("开始启动录屏软件...")
        logger.info(f"Python版本: {sys.version}")
        logger.info(f"当前工作目录: {os.getcwd()}")
        logger.info(f"程序路径: {sys.argv[0]}")
        
        # 检查必要的模块
        logger.info("检查必要模块...")
        
        try:
            import PyQt5
            logger.info(f"PyQt5版本: {PyQt5.QtCore.PYQT_VERSION_STR}")
        except ImportError as e:
            logger.error(f"导入PyQt5失败: {e}")
            return
            
        try:
            import cv2
            logger.info(f"OpenCV版本: {cv2.__version__}")
        except ImportError as e:
            logger.error(f"导入OpenCV失败: {e}")
            return
            
        try:
            import numpy
            logger.info(f"NumPy版本: {numpy.__version__}")
        except ImportError as e:
            logger.error(f"导入NumPy失败: {e}")
            return
        
        # 检查图标文件
        icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "recorder_icon.ico")
        if os.path.exists(icon_path):
            logger.info(f"找到图标文件: {icon_path}")
        else:
            logger.warning(f"图标文件不存在: {icon_path}")
        
        # 导入自定义模块
        logger.info("导入自定义模块...")
        
        try:
            import config
            logger.info("成功导入config模块")
        except ImportError as e:
            logger.error(f"导入config模块失败: {e}")
            return
            
        try:
            import utils
            logger.info("成功导入utils模块")
        except ImportError as e:
            logger.error(f"导入utils模块失败: {e}")
            return
            
        try:
            from gui import MainWindow
            logger.info("成功导入MainWindow")
        except ImportError as e:
            logger.error(f"导入MainWindow失败: {e}")
            return
        
        # 创建QApplication
        logger.info("创建QApplication...")
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtGui import QIcon
        
        app = QApplication(sys.argv)
        app.setApplicationName("功能强大的录屏软件")
        
        # 设置应用图标
        if os.path.exists(icon_path):
            app.setWindowIcon(QIcon(icon_path))
        
        # 创建主窗口
        logger.info("创建主窗口...")
        window = MainWindow()
        logger.info("显示主窗口...")
        window.show()
        
        logger.info("启动应用事件循环...")
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"程序启动失败: {str(e)}")
        logger.error(f"错误详情:\n{traceback.format_exc()}")
        
        # 显示错误对话框
        try:
            from PyQt5.QtWidgets import QApplication, QMessageBox
            if not QApplication.instance():
                app = QApplication(sys.argv)
            
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Critical)
            msg.setWindowTitle("启动错误")
            msg.setText(f"程序启动失败:\n{str(e)}")
            msg.setDetailedText(traceback.format_exc())
            msg.exec_()
        except:
            print(f"程序启动失败: {str(e)}")
            print(f"错误详情:\n{traceback.format_exc()}")
            input("按回车键退出...")

if __name__ == "__main__":
    main()
