#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试脚本
验证所有修复是否成功：
1. 帧数与设定值匹配
2. 视频时长准确
3. 音视频同步
4. 没有循环播放欺骗
"""

import os
import sys
import time
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from screen_recorder import ScreenRecorder
from audio_recorder import AudioRecorder
import utils
import cv2

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def final_comprehensive_test():
    """最终综合测试"""
    logger.info("开始最终综合测试")
    
    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(__file__), "final_test_output")
    os.makedirs(output_dir, exist_ok=True)
    
    # 测试参数
    test_fps = 30
    test_duration = 5  # 5秒测试，便于验证
    expected_frames = test_fps * test_duration  # 期望150帧
    
    # 生成测试文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    video_path = os.path.join(output_dir, f"final_test_{timestamp}.mp4")
    audio_path = os.path.join(output_dir, f"final_test_{timestamp}.wav")
    
    logger.info("=" * 60)
    logger.info("测试参数:")
    logger.info(f"  目标FPS: {test_fps}")
    logger.info(f"  录制时长: {test_duration}秒")
    logger.info(f"  期望帧数: {expected_frames}")
    logger.info(f"  视频文件: {video_path}")
    logger.info(f"  音频文件: {audio_path}")
    logger.info("=" * 60)
    
    # 创建录制器
    screen_recorder = ScreenRecorder(
        output_path=video_path,
        region=(100, 100, 1200, 800),  # 使用中等大小区域
        fps=test_fps
    )
    
    audio_recorder = AudioRecorder(
        output_path=audio_path,
        record_system_audio=True,
        record_microphone=False  # 只录制系统音频
    )
    
    try:
        # 开始录制
        logger.info("开始同步录制...")
        start_time = time.time()
        
        # 同时启动音视频录制
        screen_start = screen_recorder.start_recording()
        audio_start = audio_recorder.start_recording()
        
        if not screen_start:
            logger.error("视频录制启动失败")
            return False
            
        if not audio_start:
            logger.error("音频录制启动失败")
            return False
        
        logger.info(f"录制启动成功，开始录制 {test_duration} 秒...")
        
        # 精确等待指定时长
        time.sleep(test_duration)
        
        # 同时停止录制
        logger.info("同时停止音视频录制...")
        stop_start_time = time.time()
        
        # 先停止音频，再停止视频
        audio_recorder.stop_recording()
        screen_recorder.stop_recording()
        
        stop_end_time = time.time()
        actual_duration = stop_start_time - start_time
        
        logger.info(f"录制完成，实际时长: {actual_duration:.2f}秒")
        logger.info(f"停止录制耗时: {(stop_end_time - stop_start_time)*1000:.1f}ms")
        
        # 等待文件生成
        time.sleep(2)
        
        # 验证文件存在
        if not os.path.exists(video_path):
            logger.error(f"视频文件未生成: {video_path}")
            return False
            
        if not os.path.exists(audio_path):
            logger.error(f"音频文件未生成: {audio_path}")
            return False
        
        # 获取详细信息
        # 视频信息
        cap = cv2.VideoCapture(video_path)
        if cap.isOpened():
            video_frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            video_fps = cap.get(cv2.CAP_PROP_FPS)
            video_duration = video_frame_count / video_fps if video_fps > 0 else 0
            cap.release()
        else:
            logger.error("无法读取视频文件")
            return False
        
        # 音频信息
        audio_duration = utils.get_media_duration(audio_path)
        
        # 输出详细结果
        logger.info("=" * 60)
        logger.info("测试结果分析:")
        logger.info("-" * 30)
        logger.info("视频信息:")
        logger.info(f"  帧数: {video_frame_count} (期望: {expected_frames})")
        logger.info(f"  FPS: {video_fps:.2f} (期望: {test_fps})")
        logger.info(f"  时长: {video_duration:.2f}秒 (期望: {test_duration}秒)")
        logger.info("-" * 30)
        logger.info("音频信息:")
        logger.info(f"  时长: {audio_duration:.2f}秒 (期望: {test_duration}秒)")
        logger.info("-" * 30)
        
        # 计算准确度
        frame_accuracy = (video_frame_count / expected_frames * 100) if expected_frames > 0 else 0
        fps_accuracy = (video_fps / test_fps * 100) if test_fps > 0 else 0
        video_duration_accuracy = (video_duration / test_duration * 100) if test_duration > 0 else 0
        audio_duration_accuracy = (audio_duration / test_duration * 100) if test_duration > 0 else 0
        
        # 音视频同步度
        if audio_duration and video_duration:
            sync_diff = abs(video_duration - audio_duration)
            sync_accuracy = (1 - sync_diff / max(video_duration, audio_duration)) * 100
        else:
            sync_accuracy = 0
        
        logger.info("准确度分析:")
        logger.info(f"  帧数准确度: {frame_accuracy:.1f}%")
        logger.info(f"  FPS准确度: {fps_accuracy:.1f}%")
        logger.info(f"  视频时长准确度: {video_duration_accuracy:.1f}%")
        logger.info(f"  音频时长准确度: {audio_duration_accuracy:.1f}%")
        logger.info(f"  音视频同步度: {sync_accuracy:.1f}%")
        logger.info("=" * 60)
        
        # 评估结果
        success = True
        issues = []
        
        # 1. 帧数检查
        if frame_accuracy < 95:
            success = False
            issues.append(f"帧数准确度不足: {frame_accuracy:.1f}% (期望≥95%)")
        else:
            logger.info("✅ 帧数准确度合格")
        
        # 2. FPS检查
        if abs(video_fps - test_fps) > 2:
            issues.append(f"FPS误差过大: 设定{test_fps}, 实际{video_fps:.2f}")
        else:
            logger.info("✅ FPS准确度合格")
        
        # 3. 视频时长检查
        if abs(video_duration - test_duration) > 0.5:
            success = False
            issues.append(f"视频时长误差过大: {abs(video_duration - test_duration):.2f}秒")
        else:
            logger.info("✅ 视频时长准确度合格")
        
        # 4. 音频时长检查
        if abs(audio_duration - test_duration) > 0.5:
            issues.append(f"音频时长误差过大: {abs(audio_duration - test_duration):.2f}秒")
        else:
            logger.info("✅ 音频时长准确度合格")
        
        # 5. 音视频同步检查
        if sync_accuracy < 90:
            issues.append(f"音视频同步度不足: {sync_accuracy:.1f}% (期望≥90%)")
        else:
            logger.info("✅ 音视频同步度合格")
        
        # 输出最终结果
        logger.info("=" * 60)
        if success and len(issues) == 0:
            logger.info("🎉 所有测试完全通过！修复成功！")
            logger.info("✅ 帧数与设定值匹配")
            logger.info("✅ 视频时长准确")
            logger.info("✅ 音视频同步良好")
            logger.info("✅ 没有循环播放欺骗")
        elif success:
            logger.info("✅ 核心测试通过，有轻微问题：")
            for issue in issues:
                logger.warning(f"⚠️ {issue}")
        else:
            logger.error("❌ 测试失败，存在严重问题：")
            for issue in issues:
                logger.error(f"❌ {issue}")
        
        logger.info("=" * 60)
        return success
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理资源
        try:
            if screen_recorder.is_recording:
                screen_recorder.stop_recording()
            if audio_recorder.is_recording:
                audio_recorder.stop_recording()
        except:
            pass

def main():
    """主函数"""
    logger.info("开始最终修复验证测试")
    
    success = final_comprehensive_test()
    
    if success:
        logger.info("🎉 最终测试通过！所有问题已修复！")
        return True
    else:
        logger.error("❌ 最终测试失败！仍有问题需要解决！")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
