"""
屏幕录制模块 - 负责捕获屏幕内容并保存为视频
"""

import os
import time
import threading
import numpy as np
import cv2
import pyautogui
from PIL import ImageGrab
import logging
import subprocess
import shutil

import config
import utils

logger = logging.getLogger(__name__)

class ScreenRecorder:
    """屏幕录制类"""

    def __init__(self, output_path=None, region=None, fps=None, codec=None):
        """
        初始化屏幕录制器

        参数:
            output_path (str): 输出文件路径，如果为None则使用默认路径
            region (tuple): 录制区域 (left, top, width, height)，如果为None则全屏录制
            fps (int): 帧率，如果为None则使用默认帧率
            codec (str): 视频编码器，如果为None则使用默认编码器
        """
        self.output_path = output_path or utils.get_output_file_path()
        self.fps = fps or config.SCREEN_RECORD_CONFIG["fps"]
        self.frame_interval = 1.0 / self.fps
        self.codec = codec or config.SCREEN_RECORD_CONFIG["codec"]

        # 如果region为None，则录制全屏
        if region is None:
            screen_width, screen_height = utils.get_screen_resolution()
            self.region = (0, 0, screen_width, screen_height)
        else:
            self.region = region

        # 视频编码器
        if self.codec == "XVID" and self.output_path.lower().endswith(".mp4"):
            # 对于MP4文件，使用H.264编码器
            self.codec = "mp4v"
            logger.info(f"对MP4文件使用mp4v编码器替代XVID")
        self.fourcc = cv2.VideoWriter_fourcc(*self.codec)

        # 录制状态
        self.is_recording = False
        self.is_paused = False
        self.start_time = 0
        self.elapsed_time = 0
        self.pause_time = 0

        # 录制线程
        self.record_thread = None

        # 视频写入器
        self.out = None

        self.frame_timestamps = []

        # 确保输出目录存在
        output_dir = os.path.dirname(self.output_path)
        os.makedirs(output_dir, exist_ok=True)

        logger.info(f"初始化屏幕录制器: 输出路径={self.output_path}, 区域={self.region}, FPS={self.fps}")

    def update_fps(self, new_fps):
        """更新帧率并调整帧间隔"""
        self.fps = new_fps
        self.frame_interval = 1.0 / new_fps
        logger.info(f"更新录制帧率为: {new_fps} FPS，帧间隔调整为: {self.frame_interval:.4f} 秒")

    def start_recording(self):
        """开始录制"""
        if self.is_recording:
            logger.warning("录制已经在进行中")
            return False

        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(self.output_path)
            os.makedirs(output_dir, exist_ok=True)

            # 创建视频写入器
            width, height = self.region[2], self.region[3]
            self.out = cv2.VideoWriter(
                self.output_path,
                self.fourcc,
                self.fps,
                (width, height)
            )

            # 设置录制状态
            self.is_recording = True
            self.is_paused = False
            self.start_time = time.time()

            # 启动录制线程
            self.record_thread = threading.Thread(target=self._record_screen)
            self.record_thread.daemon = True
            self.record_thread.start()

            logger.info(f"开始录制: {self.output_path}")
            return True

        except Exception as e:
            logger.error(f"开始录制失败: {str(e)}")
            return False

    def pause_recording(self):
        """暂停录制"""
        if not self.is_recording:
            logger.warning("没有正在进行的录制")
            return False

        if self.is_paused:
            # 恢复录制
            self.is_paused = False
            self.elapsed_time += time.time() - self.pause_time
            logger.info("恢复录制")
            return True
        else:
            # 暂停录制
            self.is_paused = True
            self.pause_time = time.time()
            logger.info("暂停录制")
            return True

    def stop_recording(self):
        """
        停止录制，将缓存帧压制为视频，然后与音频合并
        """
        if not self.is_recording:
            logger.warning("没有正在进行的录制")
            return False

        self.is_recording = False
        self.is_paused = False
        if self.record_thread and self.record_thread.is_alive():
            self.record_thread.join(timeout=2.0)

        if not self.is_paused:
            self.elapsed_time += time.time() - self.start_time

        # 获取缓存文件夹路径
        cache_dir = os.path.join(os.path.dirname(self.output_path), "frame_cache")

        # 1. 将内存缓存帧压制为视频，确保帧数和时长正确
        if hasattr(self, 'frame_cache') and self.frame_cache:
            cached_frames = self.frame_cache
            logger.info(f"开始从 {len(cached_frames)} 个内存缓存帧生成视频")

            # 计算实际录制时长
            actual_duration = self.elapsed_time
            expected_frames = int(self.fps * actual_duration)
            actual_frames = len(cached_frames)

            logger.info(f"录制时长: {actual_duration:.2f}秒, 期望帧数: {expected_frames}, 实际帧数: {actual_frames}")

            # 创建视频写入器，使用设定的帧率
            width, height = self.region[2], self.region[3]
            fourcc = cv2.VideoWriter_fourcc(*self.codec)

            # 确保输出目录存在
            output_dir = os.path.dirname(self.output_path)
            os.makedirs(output_dir, exist_ok=True)

            # 创建视频写入器
            out = cv2.VideoWriter(self.output_path, fourcc, self.fps, (width, height))

            if not out.isOpened():
                logger.error(f"无法创建视频写入器: {self.output_path}")
                logger.error(f"编码器: {self.codec}, 分辨率: {width}x{height}, FPS: {self.fps}")
                return False

            # 写入所有缓存帧
            written_frames = 0
            for i, frame in enumerate(cached_frames):
                if frame is not None:
                    # 确保帧的尺寸正确
                    if frame.shape[:2] != (height, width):
                        frame = cv2.resize(frame, (width, height))
                    out.write(frame)
                    written_frames += 1

                    # 每100帧输出一次进度
                    if (i + 1) % 100 == 0:
                        logger.debug(f"已写入 {i + 1}/{len(cached_frames)} 帧")
                else:
                    logger.warning(f"第 {i} 帧为空")

            # 确保所有数据都写入文件
            out.release()

            logger.info(f"视频写入完成: 总帧数={len(cached_frames)}, 成功写入={written_frames}帧")

            # 清理内存缓存
            self.frame_cache.clear()
            logger.debug("已清理内存缓存")

            # 验证生成的视频
            cap = cv2.VideoCapture(self.output_path)
            if cap.isOpened():
                video_frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                video_fps = cap.get(cv2.CAP_PROP_FPS)
                video_duration = video_frame_count / video_fps if video_fps > 0 else 0
                cap.release()

                logger.info(f"视频生成完成: {self.output_path}")
                logger.info(f"视频信息: 帧数={video_frame_count}, FPS={video_fps:.2f}, 时长={video_duration:.2f}秒")

                # 检查视频质量
                if abs(video_duration - actual_duration) > 0.5:
                    logger.warning(f"视频时长({video_duration:.2f}s)与录制时长({actual_duration:.2f}s)差异较大")
            else:
                logger.error("无法验证生成的视频文件")
        else:
            logger.warning("没有找到内存缓存帧")

        logger.info(f"停止录制: {self.output_path}, 录制时间: {utils.format_time(self.elapsed_time)}")
        # 校验帧数与时长
        cap = cv2.VideoCapture(self.output_path)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        duration = frame_count / fps if fps > 0 else 0
        cap.release()
        logger.info(f"录制文件帧数: {frame_count}, 文件FPS: {fps}, 文件时长: {duration:.2f}秒")
        # 判断是否需要修正
        expected_frames = int(self.fps * self.elapsed_time)
        if abs(frame_count - expected_frames) > max(2, 0.05 * expected_frames):
            logger.warning(f"检测到帧数与预期不符，尝试修正视频帧率...")
            self.fix_video_fps()
        return True

    def _record_screen(self):
        """
        录制屏幕的线程函数，严格按照设定帧率录制，确保帧数与时长匹配
        核心原则：
        1. 使用固定时间间隔，确保帧数准确
        2. 每帧都必须录制，不允许跳帧
        3. 如果捕获失败，使用补帧机制
        4. 优先保证帧数正确性
        """
        logger.info(f"录制线程启动，目标帧率: {self.fps} FPS，帧间隔: {self.frame_interval:.4f}秒")
        left, top, width, height = self.region
        frame_interval = self.frame_interval
        frame_count = 0
        start_time = time.time()
        last_log_time = start_time

        # 使用内存缓存替代文件缓存，提高性能
        self.frame_cache = []  # 内存中的帧缓存

        # 清空时间戳列表
        self.frame_timestamps = []

        # 记录录制开始时间
        self.actual_start_time = start_time

        try:
            # 尝试使用mss库进行高效屏幕捕获
            try:
                import mss
                use_mss = True
                sct = mss.mss()
                monitor = {'top': top, 'left': left, 'width': width, 'height': height}
                logger.info("使用MSS库进行屏幕捕获")
            except ImportError:
                use_mss = False
                logger.warning("MSS库未安装，使用PIL.ImageGrab作为备用方案")

            # 使用定时器方式，确保严格按照帧率录制
            next_frame_time = start_time

            while self.is_recording:
                # 处理暂停状态
                if self.is_paused:
                    time.sleep(0.1)
                    # 暂停期间重新计算基准时间
                    next_frame_time = time.time()
                    continue

                current_time = time.time()

                # 等待到下一帧时间
                if current_time < next_frame_time:
                    sleep_time = next_frame_time - current_time
                    if sleep_time > 0.001:
                        time.sleep(sleep_time)

                # 无论如何都要录制这一帧，确保帧数准确
                try:
                    if use_mss:
                        # 使用MSS捕获
                        sct_img = sct.grab(monitor)
                        frame = np.array(sct_img)
                        frame = cv2.cvtColor(frame, cv2.COLOR_RGBA2BGR)
                    else:
                        # 使用PIL捕获
                        screenshot = ImageGrab.grab(bbox=(left, top, left+width, top+height))
                        frame = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

                    # 保存帧到内存缓存
                    frame_copy = frame.copy()
                    self.frame_cache.append(frame_copy)

                except Exception as e:
                    logger.error(f"捕获帧时发生错误: {str(e)}")
                    # 如果捕获失败，添加一个黑色帧或重复上一帧
                    if self.frame_cache:
                        # 重复上一帧
                        last_frame = self.frame_cache[-1].copy()
                        self.frame_cache.append(last_frame)
                    else:
                        # 创建黑色帧
                        black_frame = np.zeros((height, width, 3), dtype=np.uint8)
                        self.frame_cache.append(black_frame)

                # 记录时间戳并增加帧计数
                self.frame_timestamps.append(next_frame_time)
                frame_count += 1

                # 计算下一帧时间（严格按照帧间隔）
                next_frame_time += frame_interval

                # 定期输出进度日志
                if current_time - last_log_time >= 1.0:
                    elapsed = current_time - start_time
                    actual_fps = frame_count / elapsed if elapsed > 0 else 0
                    expected_frames = int(elapsed * self.fps)
                    frame_accuracy = (frame_count / expected_frames * 100) if expected_frames > 0 else 0

                    logger.info(f"录制进度: {frame_count}帧, 实际FPS: {actual_fps:.2f}, "
                              f"帧数准确度: {frame_accuracy:.1f}%, 当前时间: {elapsed:.2f}s")
                    last_log_time = current_time

        except Exception as e:
            logger.error(f"录制过程中发生严重错误: {str(e)}")
            self.is_recording = False
        finally:
            if use_mss and 'sct' in locals():
                try:
                    sct.close()
                except:
                    pass

            # 计算实际录制统计
            actual_end_time = time.time()
            total_time = actual_end_time - start_time
            expected_frames = int(total_time * self.fps)
            frame_accuracy = (frame_count / expected_frames * 100) if expected_frames > 0 else 0

            # 记录实际录制时长（用于音视频同步）
            self.actual_recording_duration = total_time

            logger.info(f"录制线程结束: 共录制 {frame_count} 帧, 总时长 {total_time:.2f}秒, "
                       f"期望帧数 {expected_frames}, 帧数准确度 {frame_accuracy:.1f}%")

            # 如果帧数不足，补充帧以达到期望帧数
            if frame_count < expected_frames and self.frame_cache:
                frames_to_add = expected_frames - frame_count
                logger.info(f"帧数不足，需要补充 {frames_to_add} 帧")

                # 重复最后一帧来补充
                last_frame = self.frame_cache[-1].copy()
                for i in range(frames_to_add):
                    self.frame_cache.append(last_frame.copy())
                    # 添加对应的时间戳
                    last_timestamp = self.frame_timestamps[-1] if self.frame_timestamps else start_time
                    new_timestamp = last_timestamp + frame_interval
                    self.frame_timestamps.append(new_timestamp)

                logger.info(f"已补充 {frames_to_add} 帧，总帧数: {len(self.frame_cache)}")

    def get_recording_time(self):
        """获取当前录制时间（秒）"""
        if not self.is_recording:
            return self.elapsed_time

        if self.is_paused:
            return self.elapsed_time

        current_time = time.time()
        return self.elapsed_time + (current_time - self.start_time)

    def take_screenshot(self, output_path=None):
        """
        捕获当前屏幕截图

        参数:
            output_path (str): 输出文件路径，如果为None则使用默认路径

        返回:
            str: 截图保存路径
        """
        if output_path is None:
            output_path = utils.get_output_file_path(prefix="screenshot", extension="png")

        try:
            left, top, width, height = self.region
            screenshot = ImageGrab.grab(bbox=(left, top, left+width, top+height))
            screenshot.save(output_path)
            logger.info(f"截图已保存: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"截图失败: {str(e)}")
            return None

    def get_average_frame_duration(self):
        """
        计算平均每帧保持的时长（秒）
        """
        if len(self.frame_timestamps) < 2:
            return 0
        intervals = [
            self.frame_timestamps[i+1] - self.frame_timestamps[i]
            for i in range(len(self.frame_timestamps) - 1)
        ]
        return sum(intervals) / len(intervals)

    def fix_video_fps(self, keep_original=False):
        """
        用实际帧率重新打包视频，确保播放时长与录制时长一致
        keep_original: 是否保留原始文件，默认不保留
        """
        if len(self.frame_timestamps) < 2:
            logger.warning("没有足够的时间戳数据来修正视频帧率")
            return None

        # 计算实际录制时长
        actual_duration = self.frame_timestamps[-1] - self.frame_timestamps[0]
        frame_count = len(self.frame_timestamps)

        if actual_duration > 0 and frame_count > 0:
            # 计算实际帧率
            actual_fps = frame_count / actual_duration
            logger.info(f"实际录制时长: {actual_duration:.2f}秒, 帧数: {frame_count}, 计算出的实际FPS: {actual_fps:.2f}")

            input_path = self.output_path
            if keep_original:
                output_path = input_path.replace('.mp4', '_fixed.mp4')
            else:
                output_path = input_path + '.tmp.mp4'

            ffmpeg_path = config.FORMAT_CONVERSION_CONFIG["ffmpeg_path"]

            # 使用实际帧率重新编码视频
            cmd = [
                ffmpeg_path, '-y',
                '-i', input_path,
                '-r', str(actual_fps),  # 使用计算出的实际帧率
                '-c:v', 'libx264',
                '-preset', 'medium',
                '-crf', '23',
                '-an',  # 不处理音频
                output_path
            ]

            logger.info(f"执行FFmpeg命令修正视频帧率: {' '.join(cmd)}")

            try:
                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.returncode == 0:
                    # 校验修正后的视频
                    cap = cv2.VideoCapture(output_path)
                    fixed_frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                    fixed_fps = cap.get(cv2.CAP_PROP_FPS)
                    fixed_duration = fixed_frame_count / fixed_fps if fixed_fps > 0 else 0
                    cap.release()

                    logger.info(f"修正后帧数: {fixed_frame_count}, FPS: {fixed_fps:.2f}, 时长: {fixed_duration:.2f}秒")

                    # 替换原文件
                    if not keep_original:
                        try:
                            # 先删除原文件，再重命名
                            if os.path.exists(input_path):
                                os.remove(input_path)
                            os.rename(output_path, input_path)
                            logger.info(f"已用实际fps({actual_fps:.2f})覆盖原视频: {input_path}")
                            return input_path
                        except Exception as e:
                            logger.error(f"替换原文件失败: {str(e)}")
                            logger.info(f"修正后的视频保存为: {output_path}")
                            return output_path
                    else:
                        logger.info(f"已用实际fps({actual_fps:.2f})生成新视频: {output_path}")
                        return output_path
                else:
                    logger.error(f"FFmpeg修正视频失败: {result.stderr}")
                    return None

            except Exception as e:
                logger.error(f"执行FFmpeg修正视频时发生错误: {str(e)}")
                return None
        else:
            logger.warning("无法计算实际帧率，跳过视频修正")
            return None

    def set_fps(self, new_fps):
        """设置新的帧率，并自动更新帧间隔"""
        self.fps = new_fps
        self.frame_interval = 1.0 / self.fps
        logger.info(f"帧率已更新为{self.fps}，帧间隔为{self.frame_interval:.4f}秒")
