@echo off
cd /d "%~dp0"
echo 正在启动录屏软件（调试模式）...
echo 当前目录：%CD%
echo.

if exist "dist\功能强大的录屏软件.exe" (
    echo 找到可执行文件：dist\功能强大的录屏软件.exe
    echo 文件大小：
    dir "dist\功能强大的录屏软件.exe"
    echo.
    echo 正在启动程序...
    echo 如果程序闪退，请查看下面的错误信息：
    echo.
    "dist\功能强大的录屏软件.exe"
    echo.
    echo 程序已退出，返回码：%ERRORLEVEL%
) else (
    echo 错误：找不到录屏软件可执行文件！
    echo 当前目录内容：
    dir
)

echo.
echo 按任意键退出...
pause >nul
